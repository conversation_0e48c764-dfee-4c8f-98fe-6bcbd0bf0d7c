{"env": {"browser": true, "es2021": true, "webextensions": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2021, "sourceType": "module"}, "globals": {"chrome": "readonly", "CONFIG": "readonly", "CONTENT_CONFIG": "readonly"}, "rules": {"no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "no-console": "off", "prefer-const": "error", "no-var": "error", "eqeqeq": "error", "curly": "error", "no-eval": "error", "no-implied-eval": "error", "no-new-func": "error", "no-script-url": "error", "prefer-arrow-callback": "error", "arrow-spacing": "error", "object-shorthand": "error", "prefer-template": "error", "template-curly-spacing": "error", "no-useless-concat": "error", "no-duplicate-imports": "error", "prefer-destructuring": ["error", {"array": false, "object": true}]}}