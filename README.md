# Live YT Music Lyrics

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Version](https://img.shields.io/badge/version-1.0.0-green.svg)
![Cross-Browser](https://img.shields.io/badge/browsers-Chrome%20|%20Firefox%20|%20Edge-green.svg)
![Safari](https://img.shields.io/badge/Safari-Requires%20Conversion-yellow.svg)

**Real-time synchronized lyrics for YouTube Music with customizable themes and cross-browser support.**

## ✨ Features

- 🎵 **Real-time synchronized lyrics** with precise timing
- 🖱️ **Click-to-seek** - Click any lyrics line to jump to that moment
- 🎨 **Customizable themes** and colors
- 📱 **Responsive design** for all screen sizes
- ⚡ **High performance** with optimized rendering
- 🌐 **Cross-browser compatible** (Chrome, Firefox, Edge) + Safari with conversion
- 🔒 **Privacy-focused** - no data collection
- 🎛️ **Advanced settings** - sync offset, font size, auto-scroll
- 📄 **Multiple lyrics sources** with intelligent fallback
- 🎯 **Smart lyrics matching** with duration-aware scoring
- 🌙 **Dark theme** optimized for YouTube Music

## 🚀 Installation

### Chrome/Edge/Brave
1. Download the latest release from [GitHub Releases](https://github.com/sukarth/liveytmusiclyrics/releases)
2. Extract the ZIP file
3. Open `chrome://extensions/` in your browser
4. Enable "Developer mode" (top right toggle)
5. Click "Load unpacked" and select the extracted folder
6. Navigate to [YouTube Music](https://music.youtube.com) and enjoy!

### Firefox
1. Download the latest release from [GitHub Releases](https://github.com/sukarth/liveytmusiclyrics/releases)
2. Open `about:debugging` in Firefox
3. Click "This Firefox" → "Load Temporary Add-on"
4. Select the `manifest.json` file from the extracted folder
5. Navigate to [YouTube Music](https://music.youtube.com) and enjoy!

### Safari (macOS) - Requires Additional Steps
**Note**: Safari requires conversion from Chrome extension format
1. Install Xcode from the App Store
2. Use Safari Web Extension Converter: `xcrun safari-web-extension-converter /path/to/extension`
3. Follow Apple's Web Extension guide for additional details or help

## 🎯 How to Use

1. **Install the extension** using the instructions above
2. **Open YouTube Music** in your browser
3. **Play any song** - the extension will automatically detect it
4. **Click the Lyrics tab** to view synchronized lyrics
5. **Click any lyrics line** to jump to that moment in the song
6. **Customize settings** using the ⚙️ button in the lyrics footer

### Settings & Customization

- **Sync Offset**: Fine-tune lyrics timing (-10 to +10 seconds)
- **Font Size**: Adjust lyrics text size (12-24px)
- **Theme Color**: Choose your preferred highlight color
- **Auto-scroll**: Toggle automatic scrolling to current line
- **Per-song Settings**: Save different settings for each song

## 🔧 Technical Details

### Architecture
- **Manifest V3** for modern browser compatibility
- **Modular ES6+** JavaScript with proper error handling
- **CSS Custom Properties** for dynamic theming
- **Mutation Observers** for efficient DOM monitoring
- **Service Worker** background script for API calls

### APIs Used
- **LRCLib.net** - Primary source for synchronized lyrics
- **Lyrics.ovh** - Fallback source for basic lyrics

### Performance
- **Debounced events** for smooth scrolling
- **CSS containment** for optimized rendering
- **Memory leak prevention** with proper cleanup
- **Throttled logging** to prevent console spam

## 🌟 Browser Support

| Browser | Minimum Version | Status | Notes |
|---------|----------------|--------|-------|
| Chrome  | 88+            | ✅ Fully Supported | Native Manifest V3 |
| Firefox | 109+           | ✅ Fully Supported | Manifest V3 with polyfills |
| Edge    | 88+            | ✅ Fully Supported | Chromium-based |
| Safari  | 14+            | 🟨 Requires Conversion | Needs Web Extension Converter |

## 🛠️ Development

### Prerequisites
- Node.js 16+ and npm 8+
- Modern browser for testing

### Setup
```bash
# Clone the repository
git clone https://github.com/sukarth/liveytmusiclyrics.git
cd liveytmusiclyrics

# Install dependencies (optional, for linting)
npm install

# Build extension package
npm run build
```

### Project Structure
```
liveytmusiclyrics/
├── manifest.json         # Extension configuration (Manifest V3)
├── background.js         # Service worker for API calls and lyrics fetching
├── content.js           # Main extension logic and YouTube Music integration
├── popup.html           # Extension popup interface
├── popup.js             # Popup functionality and settings management
├── styles.css           # Complete styling system with CSS custom properties
├── package.json         # Project configuration and build scripts
├── LICENSE              # MIT License
├── README.md           # Project documentation
├── CHANGELOG.md        # Version history and release notes
├── .eslintrc.json      # Code quality and linting configuration
└── .gitignore          # Git ignore patterns
```

### Code Quality
- **ESLint** configuration for consistent code style
- **JSDoc** annotations for comprehensive documentation
- **Error boundaries** for graceful failure handling
- **Modern ES6+** features throughout

## 🤝 Contributing

We welcome contributions! This is a **fully open-source project** under the MIT license.

**💡 Found this useful? Please share it with anyone who loves music and YouTube Music!**

### How to Contribute
1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Areas for Contribution
- 🌍 **Localization** - Multi-language support
- 🎨 **Themes** - Additional color schemes
- 🔧 **Features** - New functionality and improvements
- 🐛 **Bug fixes** - Issue resolution
- 📚 **Documentation** - Improved guides and examples

### Spread the Word
- ⭐ **Star this repository** if you find it useful
- 🎵 **Share with music lovers** who use YouTube Music
- 📱 **Tell your friends** about synchronized lyrics
- 🐦 **Tweet about it** to help others discover this extension

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for more details.

## 🙏 Acknowledgments

- **LRCLib.net** - Excellent synchronized lyrics API
- **Open Source Community** - For tools and inspiration
- **Contributors** - Everyone who helps improve this project

## 📞 Support

- **GitHub Issues**: [Report bugs or request features](https://github.com/sukarth/liveytmusiclyrics/issues)
- **Discussions**: [Community support and ideas](https://github.com/sukarth/liveytmusiclyrics/discussions)

## 🔄 Changelog

### v1.0.0 (2025-06-21)
- Initial release
- Real-time synchronized lyrics
- Cross-browser compatibility
- Customizable themes and settings
- Multiple lyrics sources with fallback
- Responsive design
- Privacy-focused architecture

For detailed information about each release, visit the [GitHub Releases](https://github.com/sukarth/liveytmusiclyrics/releases) page.

---

Made with ❤️ by [Sukarth Acharya](https://github.com/sukarth)