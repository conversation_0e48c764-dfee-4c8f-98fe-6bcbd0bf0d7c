/**
 * Live YT Music Lyrics - Background Service Worker
 *
 * Cross-browser lyrics fetching service with intelligent API management,
 * robust error handling, and performance optimization for real-time
 * synchronized lyrics delivery.
 *
 * <AUTHOR> Acharya
 * @version 1.0.0
 * @license MIT
 * @see {@link https://github.com/sukarth/liveytmusiclyrics}
 */

'use strict';

// ============================================================================
// CONFIGURATION - Edit these values to customize the extension
// ============================================================================

/**
 * Configuration object for API endpoints, timeouts, and system settings
 * @const {Object}
 */
const CONFIG = Object.freeze({
    // API Configuration with production-ready settings
    APIS: Object.freeze({
        LRCLIB: Object.freeze({
            BASE_URL: 'https://lrclib.net/api',
            TIMEOUT: 10000,
            MAX_RETRIES: 2,
            USER_AGENT: 'Live YT Music Lyrics v1.0.0 (https://github.com/sukarth/liveytmusiclyrics)'
        }),
        LYRICS_OVH: Object.freeze({
            BASE_URL: 'https://api.lyrics.ovh/v1',
            TIMEOUT: 5000,
            MAX_RETRIES: 1,
            USER_AGENT: 'Mozilla/5.0 (compatible; Live-YT-Music-Lyrics/1.0)'
        })
    }),

    // Performance and caching settings
    PERFORMANCE: Object.freeze({
        CACHE_TTL: 24 * 60 * 60 * 1000, // 24 hours
        MAX_CACHE_SIZE: 100,
        DEBOUNCE_DELAY: 300,
        BATCH_SIZE: 5
    }),

    // User-friendly error messages with internationalization support
    ERROR_MESSAGES: Object.freeze({
        NO_INTERNET: 'No internet connection. Please check your connection and try again.',
        NETWORK_ERROR: 'Unable to connect to lyrics services. Please check your internet connection and try again.',
        TIMEOUT: 'Lyrics search timed out. Please try again in a moment.',
        CORS_SECURITY: 'Unable to access lyrics due to browser security settings. Please try refreshing the page.',
        RATE_LIMITED: 'The lyrics service is busy. Please wait a moment and try again.',
        SERVER_ERROR: 'The lyrics service is temporarily unavailable. Please try again later.',
        INVALID_SONG_INFO: 'Song information is incomplete. Please try playing a different song.',
        NO_LYRICS_FOUND: 'No lyrics found for this song. Try playing a different version of the song, or check back later.',
        GENERIC_ERROR: 'Unable to fetch lyrics at the moment. Please try again later.'
    }),

    // Development and debugging messages
    CONSOLE: Object.freeze({
        FETCHING: '🔍 Fetching lyrics for:',
        TRYING_LRCLIB: '🎵 Trying lrclib.net API for:',
        TRYING_LYRICS_OVH: '🎵 Trying lyrics.ovh API for:',
        CLEANING_SEARCH: '🧹 Cleaned search:',
        SEARCHING: '🔍 Searching lrclib.net:',
        RESULTS_FOUND: '📄 lrclib.net search results:',
        BEST_MATCH: '🎯 Best match selected:',
        SYNCED_FOUND: '✨ Found synced lyrics from lrclib.net',
        PLAIN_FOUND: '📝 Found plain lyrics from lrclib.net',
        PARSED_LRC: '🎵 Parsed LRC:',
        API_RESPONSE: '📡 API response status:',
        API_RECEIVED: '📄 API response received, lyrics length:',
        NO_LYRICS: '❌ No lyrics found from any source',
        LRCLIB_FAILED: '❌ lrclib.net search failed:',
        LRCLIB_ERROR: '❌ lrclib.net API error:',
        LYRICS_OVH_ERROR: '❌ Lyrics.ovh API error:',
        LRCLIB_TIMEOUT: '🕐 lrclib.net request timed out, will try backup API',
        LRCLIB_NETWORK: '🌐 lrclib.net network error, will try backup API',
        LRCLIB_UNKNOWN: '❓ lrclib.net unknown error, will try backup API',
        LYRICS_OVH_TIMEOUT: '🕐 lyrics.ovh request timed out',
        LYRICS_OVH_NETWORK: '🌐 lyrics.ovh network error',
        LYRICS_OVH_UNKNOWN: '❓ lyrics.ovh unknown error',
        LYRICS_OVH_NOT_FOUND: '📭 lyrics.ovh: Song not found in database',
        LYRICS_OVH_RATE_LIMITED: '🚫 lyrics.ovh: Rate limited',
        LYRICS_OVH_SERVER_ERROR: '🔧 lyrics.ovh: Server error',
        SONG_DURATION: '⏱️ Song duration:',
        ANALYZING_RESULTS: '🎯 Analyzing',
        TOP_CANDIDATES: '🏆 Top 3 candidates:',
        SELECTED: '🎖️ SELECTED:',
        EXTENSION_INSTALLED: '🎵 YouTube Music Lyrics Extension installed'
    }),

    // Advanced scoring algorithm weights for intelligent lyrics matching
    SCORING: Object.freeze({
        TITLE_WEIGHT: 35,      // 35% weight for title similarity
        ARTIST_WEIGHT: 25,     // 25% weight for artist similarity
        DURATION_WEIGHT: 20,   // 20% weight for duration matching
        SYNCED_WEIGHT: 15,     // 15% weight for having synced lyrics
        KEYWORDS_WEIGHT: 3,    // 3% weight for special keywords
        POSITION_WEIGHT: 2     // 2% weight for search result position
    }),

    // Enhanced matching patterns for song variations
    SPECIAL_KEYWORDS: Object.freeze([
        'radio edit', 'radio version', 'radio mix',
        'extended mix', 'extended version', 'extended',
        'club mix', 'club version',
        'original mix', 'original version',
        'acoustic', 'acoustic version',
        'live', 'live version',
        'remix', 'remaster', 'remastered'
    ]),

    // Content filtering patterns
    EXPLICIT_KEYWORDS: Object.freeze(['explicit', 'clean', 'censored']),
    ATTRIBUTION_FILTERS: Object.freeze(['paroles de', 'lyrics by', 'courtesy of'])
});

// ============================================================================
// MAIN CLASS
// ============================================================================

/**
 * Advanced lyrics fetching service with intelligent caching,
 * retry mechanisms, and cross-browser compatibility
 *
 * @class LyricsService
 */
class LyricsService {
    
    /**
     * Initialize the service with performance optimizations
     */
    constructor() {
        this.cache = new Map();
        this.requestQueue = new Map();
        this.retryAttempts = new Map();
        
        this.setupMessageListener();
        this.setupPerformanceMonitoring();
        this.initializeService();
    }

    /**
     * Initialize service components with error boundaries
     * @private
     */
    initializeService() {
        try {
            this.setupCacheCleanup();
            this.validateAPIs();
            console.log(CONFIG.CONSOLE.EXTENSION_INSTALLED);
        } catch (error) {
            console.error('Service initialization failed:', error);
        }
    }

    /**
     * Set up performance monitoring and metrics collection
     * @private
     */
    setupPerformanceMonitoring() {
        this.metrics = {
            requests: 0,
            cacheHits: 0,
            errors: 0,
            avgResponseTime: 0
        };
    }

    /**
     * Advanced message listener with request queuing and deduplication
     * @private
     */
    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'fetchLyrics') {
                this.handleLyricsRequest(request, sendResponse);
                return true;
            }
            return false;
        });
    }

    /**
     * Handle lyrics requests with intelligent queuing and caching
     * @param {Object} request - Request containing song information
     * @param {Function} sendResponse - Response callback
     * @private
     */
    async handleLyricsRequest(request, sendResponse) {
        const startTime = performance.now();
        const requestId = this.generateRequestId(request.song);
        
        try {
            // Check for duplicate requests
            if (this.requestQueue.has(requestId)) {
                const existingRequest = this.requestQueue.get(requestId);
                existingRequest.callbacks.push(sendResponse);
                return;
            }

            // Create new request entry
            this.requestQueue.set(requestId, {
                callbacks: [sendResponse],
                timestamp: Date.now()
            });

            const result = await this.fetchLyricsWithCache(request.song);
            
            // Send response to all waiting callbacks
            const requestEntry = this.requestQueue.get(requestId);
            if (requestEntry) {
                requestEntry.callbacks.forEach(callback => {
                    try {
                        callback(result);
                    } catch (error) {
                        console.error('Error sending response:', error);
                    }
                });
                this.requestQueue.delete(requestId);
            }

            // Update metrics
            this.updateMetrics(startTime, true);
            
        } catch (error) {
            console.error('Error in lyrics request:', error);
            const userMessage = this.getErrorMessage(error);
            
            // Send error to all waiting callbacks
            const requestEntry = this.requestQueue.get(requestId);
            if (requestEntry) {
                requestEntry.callbacks.forEach(callback => {
                    try {
                        callback({ error: userMessage });
                    } catch (callbackError) {
                        console.error('Error sending error response:', callbackError);
                    }
                });
                this.requestQueue.delete(requestId);
            }

            this.updateMetrics(startTime, false);
        }
    }

    /**
     * Generate unique request identifier for deduplication
     * @param {Object} song - Song information
     * @returns {string} Unique request ID
     * @private
     */
    generateRequestId(song) {
        const normalizedTitle = song.title?.toLowerCase().replace(/[^\w\s]/g, '') || '';
        const normalizedArtist = song.artist?.toLowerCase().replace(/[^\w\s]/g, '') || '';
        return `${normalizedArtist}-${normalizedTitle}`;
    }

    /**
     * Update performance metrics
     * @param {number} startTime - Request start time
     * @param {boolean} success - Whether request was successful
     * @private
     */
    updateMetrics(startTime, success) {
        this.metrics.requests++;
        if (!success) {this.metrics.errors++;}
        
        const responseTime = performance.now() - startTime;
        this.metrics.avgResponseTime =
            (this.metrics.avgResponseTime * (this.metrics.requests - 1) + responseTime) / this.metrics.requests;
    }

    /**
     * Advanced caching system with TTL and size management
     * @param {Object} song - Song information
     * @returns {Promise<Object|null>} Cached lyrics or null
     * @private
     */
    async fetchLyricsWithCache(song) {
        const cacheKey = this.generateCacheKey(song);
        
        // Check cache first
        const cached = this.getCachedLyrics(cacheKey);
        if (cached) {
            this.metrics.cacheHits++;
            console.log('🎵 Using cached lyrics for:', song.title);
            return cached;
        }

        // Fetch new lyrics
        const result = await this.fetchLyrics(song);
        
        // Cache successful results
        if (result && !result.error) {
            this.setCachedLyrics(cacheKey, result);
        }

        return result;
    }

    /**
     * Generate cache key with normalization
     * @param {Object} song - Song information
     * @returns {string} Cache key
     * @private
     */
    generateCacheKey(song) {
        const normalize = str => str?.toLowerCase()
            .replace(/[^\w\s]/g, '')
            .replace(/\s+/g, ' ')
            .trim() || '';
        
        return `${normalize(song.artist)}-${normalize(song.title)}`;
    }

    /**
     * Retrieve lyrics from cache with TTL checking
     * @param {string} key - Cache key
     * @returns {Object|null} Cached data or null
     * @private
     */
    getCachedLyrics(key) {
        const cached = this.cache.get(key);
        if (!cached) {return null;}

        // Check TTL
        if (Date.now() - cached.timestamp > CONFIG.PERFORMANCE.CACHE_TTL) {
            this.cache.delete(key);
            return null;
        }

        return cached.data;
    }

    /**
     * Store lyrics in cache with automatic cleanup
     * @param {string} key - Cache key
     * @param {Object} data - Lyrics data
     * @private
     */
    setCachedLyrics(key, data) {
        // Manage cache size
        if (this.cache.size >= CONFIG.PERFORMANCE.MAX_CACHE_SIZE) {
            this.cleanupCache();
        }

        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    /**
     * Intelligent cache cleanup removing oldest entries
     * @private
     */
    cleanupCache() {
        const entries = Array.from(this.cache.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        
        // Remove oldest 25% of entries
        const toRemove = Math.floor(entries.length * 0.25);
        for (let i = 0; i < toRemove; i++) {
            this.cache.delete(entries[i][0]);
        }
    }

    /**
     * Set up periodic cache cleanup
     * @private
     */
    setupCacheCleanup() {
        setInterval(() => {
            this.cleanupExpiredCache();
        }, 60 * 60 * 1000); // Every hour
    }

    /**
     * Remove expired cache entries
     * @private
     */
    cleanupExpiredCache() {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > CONFIG.PERFORMANCE.CACHE_TTL) {
                this.cache.delete(key);
            }
        }
    }

    /**
     * Validate API endpoints at startup
     * @private
     */
    async validateAPIs() {
        try {
            // Basic connectivity check
            await Promise.allSettled([
                fetch(CONFIG.APIS.LRCLIB.BASE_URL, { method: 'HEAD' }),
                fetch(CONFIG.APIS.LYRICS_OVH.BASE_URL, { method: 'HEAD' })
            ]);
        } catch (error) {
            console.warn('API validation failed:', error);
        }
    }

    /**
     * Enhanced error message generation with context awareness
     * @param {Error|string} error - The error to convert
     * @returns {string} User-friendly error message
     * @private
     */
    getErrorMessage(error) {
        const errorMessage = error?.message || error?.toString() || 'Unknown error';
        
        // Create error categorization map for better performance
        const errorPatterns = [
            { patterns: ['NetworkError', 'fetch', 'Failed to fetch'], message: CONFIG.ERROR_MESSAGES.NETWORK_ERROR },
            { patterns: ['timeout', 'AbortError', 'aborted'], message: CONFIG.ERROR_MESSAGES.TIMEOUT },
            { patterns: ['CORS', 'blocked', 'security'], message: CONFIG.ERROR_MESSAGES.CORS_SECURITY },
            { patterns: ['429', 'rate limit', 'Too Many Requests'], message: CONFIG.ERROR_MESSAGES.RATE_LIMITED },
            { patterns: ['500', '502', '503', 'server'], message: CONFIG.ERROR_MESSAGES.SERVER_ERROR }
        ];

        for (const { patterns, message } of errorPatterns) {
            if (patterns.some(pattern => errorMessage.includes(pattern))) {
                return message;
            }
        }

        return CONFIG.ERROR_MESSAGES.GENERIC_ERROR;
    }

    /**
     * High-performance lyrics fetching with intelligent retry and fallback
     * @param {Object} songInfo - Song information object
     * @param {string} songInfo.title - Song title
     * @param {string} songInfo.artist - Song artist
     * @param {number} [songInfo.duration] - Song duration in seconds
     * @returns {Promise<Object>} Lyrics data or error
     */
    async fetchLyrics(songInfo) {
        console.log(CONFIG.CONSOLE.FETCHING, songInfo);

        // Comprehensive validation
        const validationError = this.validateSongInfo(songInfo);
        if (validationError) {
            return { error: validationError };
        }

        // Network connectivity check
        if (!navigator.onLine) {
            console.log('❌ No internet connection detected');
            return { error: CONFIG.ERROR_MESSAGES.NO_INTERNET };
        }

        try {
            // Try APIs with intelligent priority ordering
            const apiAttempts = [
                { name: 'LRCLib', method: () => this.tryLRCLibWithRetry(songInfo) },
                { name: 'LyricsOVH', method: () => this.tryLyricsOVHWithRetry(songInfo) }
            ];

            for (const { name, method } of apiAttempts) {
                try {
                    console.log(`${CONFIG.CONSOLE.TRYING_LRCLIB.replace('lrclib.net', name)}`, songInfo.title, 'by', songInfo.artist);
                    const lyrics = await method();
                    
                    if (lyrics) {
                        console.log(`✅ Success with ${name}`);
                        return { lyrics };
                    }
                } catch (apiError) {
                    console.warn(`❌ ${name} failed:`, apiError.message);
                    continue; // Try next API
                }
            }

            console.log(CONFIG.CONSOLE.NO_LYRICS);
            return { error: CONFIG.ERROR_MESSAGES.NO_LYRICS_FOUND };

        } catch (error) {
            console.error('Critical error in fetchLyrics:', error);
            return { error: this.getErrorMessage(error) };
        }
    }

    /**
     * LRCLib API with retry mechanism
     * @param {Object} songInfo - Song information
     * @returns {Promise<Object|null>} Lyrics or null
     * @private
     */
    async tryLRCLibWithRetry(songInfo) {
        const maxRetries = CONFIG.APIS.LRCLIB.MAX_RETRIES;
        let lastError;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    console.log(`🔄 LRCLib retry attempt ${attempt}/${maxRetries}`);
                    await this.delay(1000 * attempt); // Exponential backoff
                }
                
                return await this.tryLRCLib(songInfo);
            } catch (error) {
                lastError = error;
                if (attempt === maxRetries) {
                    throw error;
                }
            }
        }
        
        throw lastError;
    }

    /**
     * Lyrics.ovh API with retry mechanism
     * @param {Object} songInfo - Song information
     * @returns {Promise<Object|null>} Lyrics or null
     * @private
     */
    async tryLyricsOVHWithRetry(songInfo) {
        const maxRetries = CONFIG.APIS.LYRICS_OVH.MAX_RETRIES;
        let lastError;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    console.log(`🔄 LyricsOVH retry attempt ${attempt}/${maxRetries}`);
                    await this.delay(500 * attempt); // Shorter backoff
                }
                
                return await this.tryLyricsOVH(songInfo);
            } catch (error) {
                lastError = error;
                if (attempt === maxRetries) {
                    throw error;
                }
            }
        }
        
        throw lastError;
    }

    /**
     * Utility delay function for retry mechanisms
     * @param {number} ms - Milliseconds to delay
     * @returns {Promise<void>}
     * @private
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Validate song information before processing
     * @param {Object} songInfo - Song information to validate
     * @returns {string|null} Error message if invalid, null if valid
     * @private
     */
    validateSongInfo(songInfo) {
        if (!songInfo || typeof songInfo !== 'object') {
            console.log('❌ Invalid song information provided - not an object');
            return CONFIG.ERROR_MESSAGES.INVALID_SONG_INFO;
        }

        if (!songInfo.title || typeof songInfo.title !== 'string' || !songInfo.title.trim()) {
            console.log('❌ Invalid song title provided');
            return CONFIG.ERROR_MESSAGES.INVALID_SONG_INFO;
        }

        if (!songInfo.artist || typeof songInfo.artist !== 'string' || !songInfo.artist.trim()) {
            console.log('❌ Invalid song artist provided');
            return CONFIG.ERROR_MESSAGES.INVALID_SONG_INFO;
        }

        return null; // Valid
    }

    /**
     * Try to fetch lyrics from LRCLib API
     * @param {Object} songInfo - Song information
     * @returns {Promise<Object|null>} Lyrics data or null if failed
     * @private
     */
    async tryLRCLib(songInfo) {
        try {
            console.log(CONFIG.CONSOLE.TRYING_LRCLIB, songInfo.artist, '-', songInfo.title);

            const cleanTitle = songInfo.title.replace(/\[.*?\]/g, '').replace(/\(.*?\)/g, '').trim();
            const cleanArtist = songInfo.artist.split(',')[0].trim();

            console.log(CONFIG.CONSOLE.CLEANING_SEARCH, cleanArtist, '-', cleanTitle);

            // Add timeout to prevent hanging
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), CONFIG.APIS.LRCLIB.TIMEOUT);

            try {
                // Try search API first to find the best match
                const searchUrl = `${CONFIG.APIS.LRCLIB.BASE_URL}/search?track_name=${encodeURIComponent(cleanTitle)}&artist_name=${encodeURIComponent(cleanArtist)}`;

                console.log(CONFIG.CONSOLE.SEARCHING, searchUrl);

                const searchResponse = await fetch(searchUrl, {
                    signal: controller.signal,
                    headers: {
                        'Accept': 'application/json',
                        'User-Agent': CONFIG.APIS.LRCLIB.USER_AGENT
                    }
                });

                if (searchResponse.ok) {
                    const searchResults = await searchResponse.json();
                    console.log(CONFIG.CONSOLE.RESULTS_FOUND, searchResults.length, 'tracks found');

                    if (searchResults && searchResults.length > 0) {
                        // Find the best match using smart scoring
                        const bestMatch = this.findBestMatch(searchResults, songInfo);
                        console.log(CONFIG.CONSOLE.BEST_MATCH, bestMatch.artistName, '-', bestMatch.trackName, `(score: ${bestMatch._score})`);

                        clearTimeout(timeoutId);

                        // Check if we have synced lyrics
                        if (bestMatch.syncedLyrics && bestMatch.syncedLyrics.trim()) {
                            console.log(CONFIG.CONSOLE.SYNCED_FOUND);
                            const parsedLyrics = this.parseLRC(bestMatch.syncedLyrics);
                            parsedLyrics.source = 'lrclib.net';
                            return parsedLyrics;
                        }
                        // Fallback to plain lyrics
                        else if (bestMatch.plainLyrics && bestMatch.plainLyrics.trim()) {
                            console.log(CONFIG.CONSOLE.PLAIN_FOUND);
                            return {
                                type: 'static',
                                text: this.formatLyrics(bestMatch.plainLyrics),
                                source: 'lrclib.net'
                            };
                        }
                    }
                } else {
                    console.log(CONFIG.CONSOLE.LRCLIB_FAILED, searchResponse.status, searchResponse.statusText);
                }
            } finally {
                clearTimeout(timeoutId);
            }

            return null;
        } catch (error) {
            console.error(CONFIG.CONSOLE.LRCLIB_ERROR, error.message);

            // Don't throw here, just return null so backup API can be tried
            // The main fetchLyrics method will handle the final error messaging
            if (error.name === 'AbortError') {
                console.log(CONFIG.CONSOLE.LRCLIB_TIMEOUT);
            } else if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
                console.log(CONFIG.CONSOLE.LRCLIB_NETWORK);
            } else {
                console.log(CONFIG.CONSOLE.LRCLIB_UNKNOWN);
            }

            return null;
        }
    }

    /**
     * Try to fetch lyrics from Lyrics.ovh API as backup
     * @param {Object} songInfo - Song information
     * @returns {Promise<Object|null>} Lyrics data or null if failed
     * @private
     */
    async tryLyricsOVH(songInfo) {
        try {
            console.log(CONFIG.CONSOLE.TRYING_LYRICS_OVH, songInfo.artist, '-', songInfo.title);

            // Clean up song title (remove extra info in brackets)
            const cleanTitle = songInfo.title.replace(/\[.*?\]/g, '').replace(/\(.*?\)/g, '').trim();
            const cleanArtist = songInfo.artist.split(',')[0].trim(); // Take first artist if multiple

            console.log(CONFIG.CONSOLE.CLEANING_SEARCH, cleanArtist, '-', cleanTitle);

            // Add timeout to prevent hanging
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), CONFIG.APIS.LYRICS_OVH.TIMEOUT);

            const response = await fetch(`${CONFIG.APIS.LYRICS_OVH.BASE_URL}/${encodeURIComponent(cleanArtist)}/${encodeURIComponent(cleanTitle)}`, {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': CONFIG.APIS.LYRICS_OVH.USER_AGENT
                }
            });

            clearTimeout(timeoutId);
            console.log(CONFIG.CONSOLE.API_RESPONSE, response.status);

            if (response.ok) {
                const data = await response.json();
                console.log(CONFIG.CONSOLE.API_RECEIVED, data.lyrics ? data.lyrics.length : 0);

                if (data.lyrics && data.lyrics.trim()) {
                    return {
                        type: 'static',
                        text: this.formatLyrics(data.lyrics),
                        source: 'lyrics.ovh'
                    };
                }
            } else {
                console.log('❌ lyrics.ovh response not ok:', response.status, response.statusText);

                // Handle specific HTTP status codes
                if (response.status === 404) {
                    console.log(CONFIG.CONSOLE.LYRICS_OVH_NOT_FOUND);
                } else if (response.status === 429) {
                    console.log(CONFIG.CONSOLE.LYRICS_OVH_RATE_LIMITED);
                } else if (response.status >= 500) {
                    console.log(CONFIG.CONSOLE.LYRICS_OVH_SERVER_ERROR);
                }
            }

            return null;
        } catch (error) {
            console.error(CONFIG.CONSOLE.LYRICS_OVH_ERROR, error.message);

            // Don't throw here, let the main method handle final error messaging
            if (error.name === 'AbortError') {
                console.log(CONFIG.CONSOLE.LYRICS_OVH_TIMEOUT);
            } else if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
                console.log(CONFIG.CONSOLE.LYRICS_OVH_NETWORK);
            } else {
                console.log(CONFIG.CONSOLE.LYRICS_OVH_UNKNOWN);
            }

            return null;
        }
    }

    /**
     * Format raw lyrics text by removing timestamps and attribution
     * @param {string} rawLyrics - Raw lyrics text
     * @returns {string} Formatted lyrics
     * @private
     */
    formatLyrics(rawLyrics) {
        if (!rawLyrics) {return '';}

        return rawLyrics
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0 && !line.match(/^\[.*\]$/)) // Remove timestamp markers
            .filter(line => !CONFIG.ATTRIBUTION_FILTERS.some(filter =>
                line.toLowerCase().includes(filter)
            )) // Remove attribution lines
            .join('\n');
    }

    /**
     * Parse LRC file format into structured lyrics data
     * @param {string} lrcContent - LRC format lyrics content
     * @returns {Object} Parsed lyrics with timing information
     * @private
     */
    parseLRC(lrcContent) {
        const lines = lrcContent.split('\n');
        const lyricsLines = [];
        const metadata = {};

        for (const line of lines) {
            // Support multiple time formats: [mm:ss.xx] and [mm:ss.xxx]
            const timeMatch = line.match(/\[(\d{1,2}):(\d{2})\.(\d{2,3})\](.*)$/);
            const metaMatch = line.match(/\[([a-z]+):(.*)\]/);

            if (timeMatch) {
                const minutes = parseInt(timeMatch[1]);
                const seconds = parseInt(timeMatch[2]);
                let centiseconds = parseInt(timeMatch[3]);

                // Handle both 2-digit and 3-digit centiseconds
                if (timeMatch[3].length === 3) {
                    centiseconds = centiseconds / 10; // Convert milliseconds to centiseconds
                }

                const time = minutes * 60 + seconds + centiseconds / 100;
                const text = timeMatch[4].trim();

                if (text) {
                    lyricsLines.push({ time, text });
                }
            } else if (metaMatch) {
                metadata[metaMatch[1]] = metaMatch[2];
            }
        }

        console.log(CONFIG.CONSOLE.PARSED_LRC, lyricsLines.length, 'lines');
        lyricsLines.forEach((line, i) => {
            if (i < 3) {console.log(`📝 Line ${i + 1}: ${line.time}s - "${line.text}"`);}
        });

        return {
            type: 'synced',
            lines: lyricsLines,
            metadata
        };
    }

    /**
     * Find the best match from search results using intelligent scoring
     * @param {Array} searchResults - Array of search results
     * @param {Object} originalSongInfo - Original song information for comparison
     * @returns {Object} Best matching result with score
     * @private
     */
    findBestMatch(searchResults, originalSongInfo) {
        const cleanOriginalTitle = originalSongInfo.title.replace(/\[.*?\]/g, '').replace(/\(.*?\)/g, '').trim();
        const cleanOriginalArtist = originalSongInfo.artist.split(',')[0].trim();

        console.log(`${CONFIG.CONSOLE.ANALYZING_RESULTS} ${searchResults.length} results for: "${cleanOriginalArtist}" - "${cleanOriginalTitle}"`);

        // Use song duration from content script (parsed from .time-info element)
        const currentDuration = originalSongInfo.duration;
        if (currentDuration) {
            const minutes = Math.floor(currentDuration / 60);
            const seconds = currentDuration % 60;
            console.log(`${CONFIG.CONSOLE.SONG_DURATION} ${minutes}:${String(seconds).padStart(2, '0')} (${currentDuration}s)`);
        } else {
            console.log(`${CONFIG.CONSOLE.SONG_DURATION} unknown`);
        }

        const scoredResults = searchResults.map((result, index) => {
            const scores = this.calculateDetailedSimilarity(result, cleanOriginalTitle, cleanOriginalArtist, currentDuration, index);

            // Calculate weighted total score (out of 100)
            const totalScore =
                scores.titleScore * CONFIG.SCORING.TITLE_WEIGHT +
                scores.artistScore * CONFIG.SCORING.ARTIST_WEIGHT +
                scores.durationScore * CONFIG.SCORING.DURATION_WEIGHT +
                scores.syncedBonus * CONFIG.SCORING.SYNCED_WEIGHT +
                scores.keywordBonus * CONFIG.SCORING.KEYWORDS_WEIGHT +
                scores.positionBonus * CONFIG.SCORING.POSITION_WEIGHT;

            const hasSynced = result.syncedLyrics ? "✅ Synced" : "❌ No sync";
            const duration = result.duration ? `${Math.floor(result.duration / 60)}:${String(result.duration % 60).padStart(2, '0')}` : "Unknown";

            console.log(`📊 [${totalScore.toFixed(1)}] "${result.trackName}" by "${result.artistName}" | ${duration} | ${hasSynced}`);
            console.log(`   └─ Title:${scores.titleScore.toFixed(1)} Artist:${scores.artistScore.toFixed(1)} Duration:${scores.durationScore.toFixed(1)} Synced:${scores.syncedBonus.toFixed(1)} Keywords:${scores.keywordBonus.toFixed(1)}`);

            return {
                ...result,
                _score: totalScore,
                _breakdown: scores
            };
        });

        // Sort by score (highest first)
        scoredResults.sort((a, b) => b._score - a._score); console.log(`\n${CONFIG.CONSOLE.TOP_CANDIDATES}`);
        scoredResults.slice(0, 3).forEach((result, i) => {
            const syncedIcon = result.syncedLyrics ? '✨' : '📝';
            const duration = result.duration ? `${Math.floor(result.duration / 60)}:${String(result.duration % 60).padStart(2, '0')}` : "?:??";
            console.log(`  ${i + 1}. ${syncedIcon} [${result._score.toFixed(1)}] "${result.trackName}" by "${result.artistName}" (${duration})`);
        });

        const winner = scoredResults[0];
        console.log(`\n${CONFIG.CONSOLE.SELECTED} "${winner.trackName}" by "${winner.artistName}" (Score: ${winner._score.toFixed(1)})`);

        return winner;
    }

    /**
     * Calculate detailed similarity score between search result and original song
     * @param {Object} result - Search result to score
     * @param {string} originalTitle - Original song title
     * @param {string} originalArtist - Original song artist
     * @param {number|null} currentDuration - Current song duration in seconds
     * @param {number} position - Position in search results (0-based)
     * @returns {Object} Detailed similarity scores
     * @private
     */
    calculateDetailedSimilarity(result, originalTitle, originalArtist, currentDuration = null, position = 0) {
        const scores = {
            titleScore: 0,
            artistScore: 0,
            durationScore: 0,
            syncedBonus: 0,
            keywordBonus: 0,
            positionBonus: 0
        };

        // 1. Title similarity (normalized to 0-100)
        scores.titleScore = this.stringSimilarity(
            this.cleanForComparison(originalTitle),
            this.cleanForComparison(result.trackName)
        ) * 100;

        // 2. Artist similarity (normalized to 0-100)
        scores.artistScore = this.stringSimilarity(
            this.cleanForComparison(originalArtist),
            this.cleanForComparison(result.artistName)
        ) * 100;

        // 3. Duration similarity (normalized to 0-100)
        if (currentDuration && result.duration) {
            const durationDiff = Math.abs(currentDuration - result.duration);
            if (durationDiff <= 3) {
                scores.durationScore = 100; // Perfect match within 3 seconds
            } else if (durationDiff <= 10) {
                scores.durationScore = 90;  // Excellent match within 10 seconds
            } else if (durationDiff <= 20) {
                scores.durationScore = 75;  // Good match within 20 seconds
            } else if (durationDiff <= 45) {
                scores.durationScore = 50;  // Acceptable match within 45 seconds
            } else if (durationDiff <= 90) {
                scores.durationScore = 25;  // Poor match within 1.5 minutes
            } else {
                scores.durationScore = Math.max(0, 25 - (durationDiff - 90) / 10); // Linear decay
            }
        } else {
            scores.durationScore = 50; // Neutral score when duration unavailable
        }

        // 4. Synced lyrics bonus (0 or 100)
        scores.syncedBonus = result.syncedLyrics && result.syncedLyrics.trim() ? 100 : 0;

        // 5. Special keywords and format matching (0-100)
        scores.keywordBonus = this.calculateKeywordBonus(originalTitle, result.trackName);

        // 6. Position bonus (small preference for earlier results) (0-100)
        scores.positionBonus = Math.max(0, 100 - position * 5);

        return scores;
    }

    /**
     * Calculate bonus score for special keywords and format matching
     * @param {string} originalTitle - Original song title
     * @param {string} resultTitle - Result song title
     * @returns {number} Keyword bonus score (0-100)
     * @private
     */
    calculateKeywordBonus(originalTitle, resultTitle) {
        let bonus = 0;
        const original = originalTitle.toLowerCase();
        const result = resultTitle.toLowerCase();        // Special edition keywords
        for (const keyword of CONFIG.SPECIAL_KEYWORDS) {
            const originalHas = original.includes(keyword);
            const resultHas = result.includes(keyword);

            if (originalHas && resultHas) {
                bonus += 30; // Strong bonus for matching special keywords
            } else if (originalHas && !resultHas) {
                bonus -= 15; // Penalty for missing expected keywords
            }
        }

        // Featured artist handling
        const featRegex = /\b(feat\.?|ft\.?|featuring)\s+/gi;
        const originalHasFeat = featRegex.test(original);
        const resultHasFeat = featRegex.test(result);

        if (originalHasFeat && resultHasFeat) {
            bonus += 20; // Bonus for both having featured artists
        } else if (originalHasFeat !== resultHasFeat) {
            bonus -= 10; // Small penalty for mismatch
        }

        // Explicit/Clean version handling
        for (const word of CONFIG.EXPLICIT_KEYWORDS) {
            if (original.includes(word) && result.includes(word)) {
                bonus += 15;
            }
        }

        return Math.max(0, Math.min(100, bonus)); // Clamp between 0-100
    }    // String similarity calculation using multiple techniques  
    stringSimilarity(str1, str2) {
        if (!str1 || !str2) {return 0;}
        if (str1 === str2) {return 1;}

        // Simple similarity calculation using common words and character overlap
        const words1 = str1.split(/\s+/);
        const words2 = str2.split(/\s+/);

        // Count common words
        const commonWords = words1.filter(word => words2.includes(word)).length;
        const totalWords = Math.max(words1.length, words2.length);
        const wordSimilarity = totalWords > 0 ? (commonWords / totalWords) : 0;

        // Character similarity (Levenshtein-like)
        const maxLength = Math.max(str1.length, str2.length);
        if (maxLength === 0) {return 1;}

        let matches = 0;
        const minLength = Math.min(str1.length, str2.length);
        for (let i = 0; i < minLength; i++) {
            if (str1[i] === str2[i]) {matches++;}
        }
        const charSimilarity = matches / maxLength;

        // Combine word and character similarity
        return (wordSimilarity * 0.7) + (charSimilarity * 0.3);
    }

    /**
     * Clean string for comparison by removing special characters and normalizing spacing
     * @param {string} str - String to clean
     * @returns {string} Cleaned string
     * @private
     */
    cleanForComparison(str) {
        if (!str) {return '';}
        return str.toLowerCase()
            .replace(/[^\w\s]/g, ' ') // Replace special chars with spaces
            .replace(/\s+/g, ' ')     // Normalize multiple spaces
            .trim();
    }

    /**
     * Note: Song duration is now retrieved by the content script from .time-info element
     * and passed with the song info in the fetchLyrics message for better matching accuracy
     */
}

// ============================================================================
// SERVICE WORKER INITIALIZATION AND EVENT HANDLING
// ============================================================================

/**
 * Initialize the lyrics service when the service worker starts
 */
const lyricsService = new LyricsService();

/**
 * Service worker installation event
 */
self.addEventListener('install', (event) => {
    console.log('🎵 Live YT Music Lyrics service worker installed');
    event.waitUntil(
        Promise.all([
            self.skipWaiting(),
            lyricsService.validateAPIs().catch(error =>
                console.warn('API validation during install failed:', error)
            )
        ])
    );
});

/**
 * Service worker activation event
 */
self.addEventListener('activate', (event) => {
    console.log('🎵 Live YT Music Lyrics service worker activated');
    event.waitUntil(self.clients.claim());
});

/**
 * Handle extension installation
 */
chrome.runtime.onInstalled.addListener(() => {
    console.log(CONFIG.CONSOLE.EXTENSION_INSTALLED);
});

/**
 * Performance monitoring
 */
if (typeof performance !== 'undefined') {
    setInterval(() => {
        const {metrics} = lyricsService;
        if (metrics.requests > 0) {
            console.log('📊 Performance:', {
                requests: metrics.requests,
                cacheHitRate: `${((metrics.cacheHits / metrics.requests) * 100).toFixed(1)}%`,
                avgResponseTime: `${metrics.avgResponseTime.toFixed(0)}ms`,
                errorRate: `${((metrics.errors / metrics.requests) * 100).toFixed(1)}%`
            });
        }
    }, 5 * 60 * 1000);
}
