{"name": "live-yt-music-lyrics", "version": "1.0.0", "description": "Cross-browser extension providing real-time synchronized lyrics overlay for YouTube Music with customizable themes and settings", "main": "background.js", "scripts": {"build": "npm run lint && npm run package", "package": "7z a -tzip live-yt-music-lyrics-v1.0.0.zip . -x!node_modules -x!.git -x!*.zip -x!package-lock.json", "lint": "eslint *.js --fix || echo ESLint not installed", "test": "echo Tests will be added in future versions", "clean": "powershell -Command \"Remove-Item *.zip -ErrorAction SilentlyContinue\""}, "keywords": ["youtube-music", "lyrics", "live-lyrics", "autoscrolling lyrics", "browser-extension", "chrome", "firefox", "edge", "music", "synchronized-lyrics", "lrc", "real-time", "open-source"], "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/sukarth"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sukarth/liveytmusiclyrics.git"}, "bugs": {"url": "https://github.com/sukarth/liveytmusiclyrics/issues"}, "homepage": "https://github.com/sukarth/liveytmusiclyrics#readme", "devDependencies": {"eslint": "^8.0.0"}}