/**
 * ========================================================================
 * YouTube Music Live Lyrics Extension - Stylesheet
 * ========================================================================
 *
 * <AUTHOR> Acharya
 * @version 1.0.0
 *
 * A comprehensive stylesheet for the YouTube Music Live Lyrics Extension
 * that provides synchronized lyrics overlay with customizable themes,
 * responsive design, and accessibility features.
 *
 * Features:
 * - Dynamic theme color system with CSS custom properties
 * - Responsive design for all screen sizes
 * - Accessibility support (high contrast, reduced motion)
 * - Smooth animations and transitions
 * - Settings modal with interactive controls
 * - Toast notifications system
 */

/* ========================
   CSS Custom Properties
   ======================== */

/**
 * Root CSS custom properties that define the theme color system.
 * These are dynamically updated by JavaScript when users change theme colors.
 */
:root {
    /* Default theme color (red) - can be customized by users */
    --theme-color: #ff6b35;
    --theme-color-light: #ff8c66;
    --theme-color-dark: #cc5628;
    
    /* Alpha variants for transparency effects */
    --theme-color-alpha-10: rgba(255, 107, 53, 0.1);
    --theme-color-alpha-15: rgba(255, 107, 53, 0.15);
    --theme-color-alpha-20: rgba(255, 107, 53, 0.2);
    --theme-color-alpha-30: rgba(255, 107, 53, 0.3);
    --theme-color-alpha-40: rgba(255, 107, 53, 0.4);
    --theme-color-alpha-50: rgba(255, 107, 53, 0.5);
    --theme-color-glow: rgba(255, 107, 53, 0.7);
    
    /* Legacy support for existing implementations */
    --lyrics-highlight-color: var(--theme-color);
    
    /* Layout and timing constants */
    --border-radius-small: 4px;
    --border-radius-medium: 8px;
    --border-radius-large: 12px;
    --border-radius-xl: 16px;
    --transition-fast: 0.15s ease-out;
    --transition-medium: 0.3s ease-out;
    --transition-slow: 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========================
   Tab Renderer & Overlay
   ======================== */
/* ========================
   Tab Renderer & Overlay
   ======================== */

/**
 * Tab renderer positioning - provides the foundation for lyrics overlay
 */
#tab-renderer {
    position: relative;
    contain: layout style paint; /* CSS containment for better performance */
}

/**
 * Main lyrics wrapper - creates full-screen overlay when lyrics tab is active
 */
#tab-renderer > .ytmusic-lyrics-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    background: #030303;
    margin: 0;
    display: flex;
    flex-direction: column;
    contain: layout style paint;
}

/**
 * Legacy content container - maintained for compatibility
 */
#tab-renderer > .ytmusic-lyrics-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    background: #030303;
    margin: 0;
    flex: 1;
    contain: layout style paint;
}

/**
 * Section List Management
 * Controls when to hide/show YouTube Music's default section list
 * to make room for the lyrics overlay
 */

/* Force hide section list when explicitly set to display: none */
#tab-renderer > ytmusic-section-list-renderer[style*="display: none"] {
    display: none !important;
}

/* Hide section list when lyrics wrapper is present (modern browsers with :has support) */
#tab-renderer:has(> .ytmusic-lyrics-wrapper) > ytmusic-section-list-renderer[page-type="MUSIC_PAGE_TYPE_TRACK_LYRICS"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

/* Hide section list when lyrics wrapper follows it (fallback for browsers without :has) */
#tab-renderer > ytmusic-section-list-renderer[page-type="MUSIC_PAGE_TYPE_TRACK_LYRICS"]:has(~ .ytmusic-lyrics-wrapper) {
    display: none !important;
}

/* Hide section list when data attribute indicates lyrics are active */
#tab-renderer[data-lyrics-active="true"] > ytmusic-section-list-renderer[page-type="MUSIC_PAGE_TYPE_TRACK_LYRICS"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Restore visibility for related tracks page - should never be hidden by our extension */
#tab-renderer > ytmusic-section-list-renderer[page-type="MUSIC_PAGE_TYPE_TRACK_RELATED"]:not([style*="display: none"]) {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    position: static !important;
    left: auto !important;
}

/* ========================
   Main Lyrics Container
   ======================== */

/**
 * Primary container for lyrics content with optimized scrolling
 * and responsive padding that adapts to different screen sizes
 */
.ytmusic-lyrics-content {
    /* Layout and spacing */
    padding: 5% 3% 8%;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
    position: relative;
    
    /* Scrolling behavior */
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;
    
    /* Firefox scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: rgba(120, 120, 120, 0.3) transparent;
    
    /* Performance optimizations */
    will-change: scroll-position;
    contain: layout style paint;
}

/**
 * WebKit/Chromium scrollbar styling
 * Creates a minimal, theme-aware scrollbar
 */
.ytmusic-lyrics-content::-webkit-scrollbar {
    width: 2px;
}

.ytmusic-lyrics-content::-webkit-scrollbar-track {
    background: transparent;
}

.ytmusic-lyrics-content::-webkit-scrollbar-thumb {
    background: rgba(120, 120, 120, 0.3);
    border-radius: var(--border-radius-medium);
    border: none;
    transition: background var(--transition-fast);
}

.ytmusic-lyrics-content::-webkit-scrollbar-thumb:hover {
    background: var(--theme-color-alpha-50);
}

/* Hide scrollbar buttons completely */
.ytmusic-lyrics-content::-webkit-scrollbar-button {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
}

/* ========================
   Lyrics Footer
   ======================== */

/**
 * Footer section that shows lyrics source and settings button
 * Positioned at the bottom of the lyrics wrapper
 */
.ytmusic-lyrics-source {
    /* Layout */
    position: static;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    
    /* Styling */
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    
    /* Typography */
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
    font-weight: 400;
}

/**
 * Settings button within the lyrics footer
 * Provides access to lyrics customization options
 */
.ytmusic-lyrics-source .settings-button {
    /* Layout */
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    
    /* Styling */
    background: var(--theme-color-alpha-10);
    border: 1px solid var(--theme-color-alpha-20);
    border-radius: var(--border-radius-small);
    
    /* Typography */
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    
    /* Interactions */
    cursor: pointer;
    pointer-events: auto;
    transition: all var(--transition-fast);
    user-select: none;
}

.ytmusic-lyrics-source .settings-button:hover {
    background: var(--theme-color-alpha-20);
    border-color: var(--theme-color-alpha-40);
    color: rgba(255, 255, 255, 0.9);
    transform: scale(1.05);
}

.ytmusic-lyrics-source .settings-button:active {
    transform: scale(0.98);
}

/* ========================
   Lyrics Lines
   ======================== */

/**
 * Individual lyrics line styling with smooth transitions
 * Supports multiple states: default, hover, active, passed
 */
.ytmusic-lyrics-line {
    /* Layout and spacing */
    padding: 12px 24px;
    margin: 20px auto;
    max-width: 80%;
    position: relative;
    
    /* Typography */
    font-size: 16px;
    line-height: 1.5;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.4);
    letter-spacing: 0.02em;
    
    /* Text handling */
    word-wrap: break-word;
    white-space: normal;
    hyphens: auto;
    text-align: center;
    
    /* Visual styling */
    border-radius: var(--border-radius-large);
    backdrop-filter: blur(10px);
    opacity: 0.7;
    
    /* Interactions */
    cursor: pointer;
    user-select: none;
    
    /* Animations */
    transition: all var(--transition-fast) cubic-bezier(0.4, 0.0, 0.2, 1);
    animation: fadeInUp 0.3s ease-out;
    
    /* Performance */
    will-change: transform, opacity, color;
    contain: layout style paint;
}

.ytmusic-lyrics-line:hover {
    background: var(--theme-color-alpha-10);
    color: rgba(255, 255, 255, 0.8);
    transform: translateX(4px);
    opacity: 0.9;
    cursor: pointer;
}

.ytmusic-lyrics-line.active {
    background: var(--theme-color-alpha-15);
    color: #fff;
    font-weight: 500;
    transform: scale(1.05) translateX(8px);
    position: relative;
    box-shadow:
        0 8px 25px var(--theme-color-alpha-30),
        0 -4px 15px var(--theme-color-alpha-20),
        0 0 25px var(--theme-color-alpha-20);
    border: none;
    border-radius: 8px;
    font-size: 17px;
    z-index: 10;
    text-shadow:
        0 0 15px var(--theme-color-glow),
        0 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.3px;
    opacity: 1;
    transition: all 0.15s ease-out;
}

.ytmusic-lyrics-line.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--lyrics-highlight-color, var(--theme-color));
    opacity: 0.15;
    border-radius: 8px;
    z-index: -1;
    transition: all 0.15s ease-out;
}

.ytmusic-lyrics-line.active::before {
    content: '';
    position: absolute;
    top: -16px;
    left: -28px;
    right: -28px;
    bottom: -16px;
    background: radial-gradient(ellipse at center,
            var(--lyrics-highlight-color, var(--theme-color)) 0%,
            transparent 70%);
    opacity: 0.2;
    border-radius: 16px;
    z-index: -1;
}

.ytmusic-lyrics-line.passed {
    color: rgba(255, 255, 255, 0.25);
    transform: scale(0.96);
    font-size: 15px;
    opacity: 0.5;
}

/* ========================
   Static Lyrics & States
   ======================== */
.ytmusic-static-lyrics {
    white-space: pre-line;
    font-size: 16px;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.9);
    padding: 16px;
    text-align: center;
}

.ytmusic-lyrics-loading {
    text-align: center;
    color: var(--theme-color);
    font-size: 16px;
    padding: 40px 20px;
    animation: pulse 2s infinite;
    font-weight: 500;
}

.ytmusic-lyrics-error {
    text-align: center;
    color: #ff4444;
    padding: 40px 20px;
    font-size: 16px;
    font-weight: 500;
}

/* ========================
   Settings Modal
   ======================== */
.ytmusic-settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: all 0.3s ease-out;
}

.ytmusic-settings-modal.fade-out {
    opacity: 0 !important;
    background: rgba(0, 0, 0, 0) !important;
    backdrop-filter: blur(0px) !important;
    transition: all 0.3s ease-in;
}

.ytmusic-settings-content {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid var(--theme-color-alpha-30);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    width: 480px;
    max-width: 90vw;
    max-height: 80vh;
    overflow: hidden;
    font-family: 'YouTube Sans', 'Roboto', sans-serif;
    position: relative;
    transform: scale(1) translateY(0);
    opacity: 1;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.ytmusic-settings-modal.fade-out .ytmusic-settings-content {
    transform: scale(0.7) translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease-in;
}

/* ========================
   Settings Modal Header
   ======================== */
.ytmusic-settings-header {
    padding: 20px 24px;
    background: linear-gradient(90deg, var(--theme-color-alpha-10) 0%, var(--theme-color-alpha-50) 100%);
    border-bottom: 1px solid var(--theme-color-alpha-20);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.ytmusic-settings-title {
    font-size: 20px;
    font-weight: 600;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 8px;
}

.ytmusic-settings-close {
    background: var(--theme-color-alpha-10);
    border: 1px solid var(--theme-color-alpha-20);
    color: rgba(255, 255, 255, 0.7);
    width: 32px;
    height: 32px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.2s ease;
}

.ytmusic-settings-close:hover {
    background: var(--theme-color-alpha-30);
    border-color: var(--theme-color-alpha-50);
    color: #fff;
    transform: scale(1.1);
}

/* ========================
   Settings Modal Body
   ======================== */
.ytmusic-settings-body {
    padding: 24px;
    max-height: 60vh;
    overflow: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--theme-color-alpha-30) transparent;
}

.ytmusic-settings-body::-webkit-scrollbar {
    width: 6px;
}

.ytmusic-settings-body::-webkit-scrollbar-track {
    background: transparent;
}

.ytmusic-settings-body::-webkit-scrollbar-thumb {
    background: var(--theme-color-alpha-30);
    border-radius: 3px;
}

.ytmusic-settings-body::-webkit-scrollbar-thumb:hover {
    background: var(--theme-color-alpha-50);
}

/* ========================
   Settings Groups & Labels
   ======================== */
.ytmusic-settings-group {
    margin-bottom: 24px;
}

.ytmusic-settings-group:last-child {
    margin-bottom: 0;
}

.ytmusic-settings-label {
    display: block;
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    user-select: none;
}

/* ========================
   Slider Controls
   ======================== */
.ytmusic-settings-slider-group {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.ytmusic-settings-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.1);
    outline: none;
    cursor: pointer;
    appearance: none;
}

.ytmusic-settings-slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--theme-color);
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px var(--theme-color-alpha-40);
    transition: all 0.2s ease;
}

.ytmusic-settings-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px var(--theme-color-alpha-50);
}

.ytmusic-settings-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--theme-color);
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px var(--theme-color-alpha-40);
    transition: all 0.2s ease;
}

.ytmusic-settings-value {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: #fff;
    padding: 6px 10px;
    font-size: 12px;
    text-align: center;
    min-width: 60px;
    outline: none;
    transition: all 0.2s ease;
}

.ytmusic-settings-value:focus {
    border-color: var(--theme-color-alpha-50);
    background: var(--theme-color-alpha-10);
}

/* ========================
   Toggle Switch
   ======================== */
.ytmusic-settings-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.ytmusic-toggle-switch {
    position: relative;
    width: 48px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.ytmusic-toggle-switch.active {
    background: var(--theme-color);
    box-shadow: 0 0 12px var(--theme-color-alpha-40);
    border-color: var(--theme-color);
}

.ytmusic-toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #fff;
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ytmusic-toggle-switch.active::after {
    transform: translateX(24px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* ========================
   Color Picker
   ======================== */
.ytmusic-settings-color {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.ytmusic-color-picker {
    width: 48px;
    height: 32px;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    outline: none;
    transition: all 0.2s ease;
}

.ytmusic-color-picker:hover {
    border-color: var(--theme-color-alpha-50);
    transform: scale(1.05);
}

.ytmusic-color-value {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: #fff;
    padding: 6px 10px;
    font-size: 12px;
    font-family: monospace;
    min-width: 80px;
    outline: none;
    transition: all 0.2s ease;
}

.ytmusic-color-value:focus {
    border-color: var(--theme-color-alpha-50);
    background: var(--theme-color-alpha-10);
}

/* ========================
   Action Buttons
   ======================== */
.ytmusic-settings-actions {
    padding: 20px 24px;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.ytmusic-settings-btn {
    background: var(--theme-color-alpha-10);
    border: 1px solid var(--theme-color-alpha-30);
    color: #fff;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    font-family: inherit;
    white-space: nowrap;
    flex: 1;
    min-width: 140px;
    text-align: center;
}

.ytmusic-settings-btn:hover {
    background: var(--theme-color-alpha-20);
    border-color: var(--theme-color-alpha-50);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--theme-color-alpha-30);
}

.ytmusic-settings-btn.primary {
    background: var(--theme-color);
    border-color: var(--theme-color);
}

.ytmusic-settings-btn.primary:hover {
    background: var(--theme-color-dark);
    border-color: var(--theme-color-dark);
}

.ytmusic-settings-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
}

.ytmusic-settings-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    color: #fff;
}

/* ========================
   Toast Notifications
   ======================== */
.ytmusic-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, var(--theme-color) 0%, var(--theme-color-dark) 100%);
    color: #fff;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 8px 32px var(--theme-color-alpha-40);
    backdrop-filter: blur(10px);
    z-index: 10001;
    font-family: 'YouTube Sans', 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 500;
    max-width: 300px;
    word-wrap: break-word;
    animation: slideInRight 0.3s ease-out;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ytmusic-toast.slide-out {
    animation: slideOutRight 0.3s ease-in forwards;
}

/* ========================
   Animations & Keyframes
   ======================== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 0.6;
        transform: translateY(0);
    }
}

@keyframes pulse {

    0%,
    100% {
        opacity: 0.6;
    }

    50% {
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }

    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* ========================
   Responsive Design
   ======================== */
@media (max-width: 600px) {
    .ytmusic-settings-content {
        width: 95vw;
        margin: 20px;
    }

    .ytmusic-settings-actions {
        flex-direction: column;
    }

    .ytmusic-settings-btn {
        min-width: auto;
        flex: none;
    }

    .ytmusic-settings-slider-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .ytmusic-settings-value {
        align-self: center;
        min-width: 80px;
    }
}

/* ========================
   Accessibility & Preferences
   ======================== */
@media (prefers-contrast: high) {
    .ytmusic-settings-modal {
        background: rgba(0, 0, 0, 0.9);
    }

    .ytmusic-settings-content {
        border-width: 3px;
        background: #000;
    }

    .ytmusic-settings-slider::-webkit-slider-thumb {
        border-color: #000;
    }
}

@media (prefers-reduced-motion: reduce) {

    .ytmusic-settings-modal,
    .ytmusic-settings-content {
        transition-duration: 0.1s;
    }

    .ytmusic-toast,
    .ytmusic-settings-btn,
    .ytmusic-settings-close {
        animation: none;
        transition-duration: 0.1s;
    }

    .ytmusic-toggle-switch,
    .ytmusic-toggle-switch::after {
        transition-duration: 0.1s;
    }
}

/* ========================
   Dark Theme Adjustments
   ======================== */
[dark] .ytmusic-settings-content {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    border-color: var(--theme-color-alpha-40);
}